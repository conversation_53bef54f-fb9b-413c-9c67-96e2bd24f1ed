import { <PERSON>em<PERSON><PERSON><PERSON>, Plugin, WorkspaceLeaf, ViewStateResult, Notice, MarkdownView, TFile, View } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';

import { Logger, createLogger, LogLevel, setLogLevel } from './logger';
import { MindMapPluginSettings, DEFAULT_SETTINGS, MindMapSettingTab } from './settings';
import { MindMapNode, MindMapViewState, MIND_MAP_VIEW_TYPE, generateUniqueId, reconstructNodeTree, findNodeById, getAllNodes } from './models';
import { MarkdownConverter } from './markdown-converter';

/**
 * 思维导图插件主类
 */
export default class MindMapPlugin extends Plugin {
    public transformer: Transformer;
    public mindmap: Markmap | null = null;
    public rootNode: MindMapNode | null = null;

    public editingNode: MindMapNode | null = null;
    public selectedNode: MindMapNode | null = null;
    public isSyncing: boolean = false; // 标志是否正在同步，避免循环
    markdownConverter: MarkdownConverter;
    syncTimeouts: Record<string, NodeJS.Timeout> = {}; // 存储同步超时ID
    
    // 插件设置
    settings: MindMapPluginSettings;
    
    // 日志记录器
    logger: Logger;

    // 视图注册状态标志
    private viewRegistered: boolean = false;

    async onload() {
        // 加载设置
        this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
        
        // 初始化日志记录器
        this.logger = createLogger('MindMapPlugin');
        setLogLevel(this.settings.logLevel);
        this.logger.info('插件加载中...');
        
        // 初始化转换器
        this.transformer = new Transformer();
        this.markdownConverter = new MarkdownConverter();
        
            // 初始化辅助方法
        this.initializeHelperMethods();

        // 注册视图（避免重复注册）
        if (!this.viewRegistered) {
            try {
                this.registerView(
                    MIND_MAP_VIEW_TYPE,
                    (leaf: WorkspaceLeaf) => {
                        return new MindMapView(leaf, this) as unknown as View;
                    }
                );
                this.viewRegistered = true;
                this.logger.debug('视图类型注册成功');
            } catch (error) {
                this.logger.warn('视图类型注册失败，可能已存在', error);
            }
        }

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加切换命令
        this.addCommand({
            id: 'toggle-mindmap-markdown',
            name: '切换思维导图/Markdown视图',
            hotkeys: [{ modifiers: ['Ctrl'], key: 'M' }],
            callback: () => this.toggleMindMapMarkdown()
        });

        // 添加导入导出命令
        this.addCommand({
            id: 'export-to-markdown',
            name: '导出为Markdown文档',
            callback: () => this.exportToMarkdown()
        });

        this.addCommand({
            id: 'import-from-markdown',
            name: '从Markdown文档导入',
            callback: () => this.importFromMarkdown()
        });

        // 添加节点操作命令
        this.addCommand({
            id: 'create-child-node',
            name: '创建子节点',
            callback: () => {
                if (this.selectedNode) {
                    this.createChildNode(this.selectedNode);
                }
            }
        });

        this.addCommand({
            id: 'create-sibling-node',
            name: '创建同级节点',
            callback: () => {
                if (this.selectedNode) {
                    this.createSiblingNode(this.selectedNode);
                }
            }
        });

        this.addCommand({
            id: 'delete-node',
            name: '删除节点',
            callback: () => {
                if (this.selectedNode && this.selectedNode !== this.rootNode) {
                    this.deleteNode(this.selectedNode);
                }
            }
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.getMindMapView();
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing().catch(console.error);
                    } else {
                        this.startEditing(this.selectedNode).catch(console.error);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey && this.selectedNode !== this.rootNode) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
        
        // 添加设置标签页
        this.addSettingTab(new MindMapSettingTab(this.app, this));
        
        this.logger.info('插件加载完成');
    }

    onunload() {
        this.logger.info('插件卸载中...');

        // 清除所有同步超时
        Object.values(this.syncTimeouts).forEach(timeout => {
            clearTimeout(timeout);
        });

        // 关闭所有思维导图视图
        this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE).forEach(leaf => {
            this.closeMindMapView(leaf);
        });

        // 重置视图注册标志
        this.viewRegistered = false;

        this.logger.info('插件卸载完成');
    }
    
    // 保存设置
    async saveSettings() {
        await this.saveData(this.settings as any);
        this.logger.info('设置已保存');
    }

    // 创建新的思维导图
    async createNewMindMap() {
        // 创建默认根节点
        this.rootNode = {
            id: generateUniqueId(),
            content: '中心主题',
            children: [],
            isExpanded: true,
            nodeType: 'root'
        };

        // 创建新的叶子
        const leaf = this.createNewLeaf();
        
        // 设置视图状态
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        // 显示叶子
        this.app.workspace.revealLeaf(leaf);
        
        this.logger.info('创建了新的思维导图');
    }

    // 切换思维导图和Markdown视图
    async toggleMindMapMarkdown() {
        const activeLeaf = this.app.workspace.activeLeaf;
        if (!activeLeaf) return;
        
        const activeView = activeLeaf.view;
        
        // 从Markdown视图切换到思维导图视图
        if (activeView instanceof MarkdownView) {
            const file = activeView.file;
            if (!file) return;
            
            const content = await this.app.vault.read(file);
            const mindMapData = this.markdownConverter.parseMarkdownToMindMap(content, file.name);
            
            // 创建新的叶子
            const leaf = this.createNewLeaf();
            
            // 设置视图状态
            await leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: { 
                    data: mindMapData,
                    sourceFile: file.path
                }
            });
            
            // 显示叶子
            this.app.workspace.revealLeaf(leaf);
            
            this.logger.info('从Markdown切换到思维导图视图');
        } 
        // 从思维导图视图切换到Markdown视图
        else if (activeView instanceof MindMapView) {
            const state = activeView.getState();
            const sourceFile = state.sourceFile as string;

            if (sourceFile) {
                // 打开已有的源文件
                const file = this.app.vault.getAbstractFileByPath(sourceFile);
                if (file instanceof TFile) {
                    await this.app.workspace.openLinkText(file.path, '', true);
                    this.logger.info('从思维导图切换到Markdown视图');
                } else {
                    new Notice('源文件不存在，将创建新文件');
                    await this.exportToMarkdown();
                }
            } else if (this.rootNode) {
                // 导出为新的Markdown文件
                await this.exportToMarkdown();
            } else {
                new Notice('没有可用的思维导图数据');
            }
        }
    }

    // 从Markdown文档导入
    async importFromMarkdown() {
        const activeLeaf = this.app.workspace.activeLeaf;
        if (!activeLeaf) return;
        
        const activeView = activeLeaf.view;
        
        if (activeView instanceof MarkdownView) {
            const file = activeView.file;
            if (!file) return;
            
            const content = await this.app.vault.read(file);
            const mindMapData = this.markdownConverter.parseMarkdownToMindMap(content, file.name);
            
            // 创建新的叶子
            const leaf = this.createNewLeaf();
            
            // 设置视图状态
            await leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: { 
                    data: mindMapData,
                    sourceFile: file.path
                }
            });
            
            // 显示叶子
            this.app.workspace.revealLeaf(leaf);
            
            this.logger.info('从Markdown导入到思维导图');
        } else {
            new Notice('请先打开一个Markdown文件');
        }
    }

    // 导出为Markdown文档
    async exportToMarkdown() {
        if (!this.rootNode) {
            new Notice('没有可导出的思维导图数据');
            return;
        }
        
        // 生成Markdown内容
        const markdown = this.markdownConverter.generateMarkdownFromMindMap(this.rootNode);
        
        // 创建新文件
        const fileName = `${this.rootNode.content || 'MindMap'}.md`;
        const sanitizedFileName = this.sanitizeFileName(fileName);
        
        try {
            // 检查文件是否存在
            const exists = await this.fileExists(sanitizedFileName);
            
            if (exists) {
                const shouldOverwrite = await this.confirmOverwrite(sanitizedFileName);
                if (!shouldOverwrite) return;
            }
            
            // 创建文件
            const file = await this.app.vault.create(sanitizedFileName, markdown);
            
            // 打开文件
            await this.app.workspace.openLinkText(file.path, '', true);
            
            // 更新当前思维导图视图的源文件
            const activeView = this.getMindMapView();
            if (activeView) {
                const state = activeView.getState();
                state.sourceFile = file.path;
                activeView.setState(state, { history: false });
            }
            
            new Notice(`思维导图已导出为 ${sanitizedFileName}`);
            this.logger.info(`思维导图已导出为 ${sanitizedFileName}`);
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            new Notice(`导出失败: ${errorMessage}`);
            this.logger.error('导出失败', error);
        }
    }

    // 清理文件名
    private sanitizeFileName(name: string): string {
        return name.replace(/[\\/:*?"<>|]/g, '_');
    }

    // 安全地获取错误消息
    public getErrorMessage(error: unknown): string {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        return '未知错误';
    }

    // 创建新的工作区叶子
    private createNewLeaf(): WorkspaceLeaf {
        return this.app.workspace.getLeaf(
            this.settings.defaultSplitMode === 'split' ? 'split' : true
        );
    }

    // 安全执行异步操作的包装器
    private async safeExecute<T>(
        operation: () => Promise<T>,
        operationName: string,
        showNotice: boolean = true
    ): Promise<T | null> {
        try {
            return await operation();
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.logger.error(`${operationName}失败`, error);

            if (showNotice) {
                new Notice(`${operationName}失败: ${errorMessage}`);
            }

            return null;
        }
    }

    // 检查文件是否存在
    private async fileExists(filePath: string): Promise<boolean> {
        try {
            const file = this.app.vault.getAbstractFileByPath(filePath);
            return !!file;
        } catch (error) {
            this.logger.warn('检查文件存在性时出错', error);
            return false;
        }
    }

    // 确认覆盖文件
    private async confirmOverwrite(fileName: string): Promise<boolean> {
        return new Promise((resolve) => {
            const notice = new Notice(
                `文件 ${fileName} 已存在。是否覆盖？`,
                0
            );
            
            // 添加按钮
            notice.noticeEl.createEl('button', { text: '覆盖' }).addEventListener('click', () => {
                notice.hide();
                resolve(true);
            });
            
            notice.noticeEl.createEl('button', { text: '取消' }).addEventListener('click', () => {
                notice.hide();
                resolve(false);
            });
        });
    }

    // 初始化辅助方法
    private initializeHelperMethods() {
        // 获取MindMapView的辅助方法
        this.getMindMapView = () => {
            const leaves = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
            if (leaves.length === 0) return null;
            const leaf = leaves[0];
            return leaf.view as unknown as MindMapView;
        };
    }

    // 获取MindMapView的辅助方法
    getMindMapView(): MindMapView | null {
        const leaves = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        if (leaves.length === 0) return null;
        const leaf = leaves[0];
        return leaf.view as unknown as MindMapView;
    }
    
    // 从源文件更新思维导图
    async updateFromSourceFile(file: TFile) {
        if (this.isSyncing) return;
        
        try {
            this.isSyncing = true;
            
            // 读取文件内容
            const content = await this.app.vault.read(file);
            
            // 解析为思维导图数据
            const newRoot = this.markdownConverter.parseMarkdownToMindMap(content, file.name);
            
            // 保存旧节点状态
            if (this.rootNode && this.settings.preserveNodeStates) {
                this.preserveNodeStates(this.rootNode, newRoot);
            }
            
            // 更新根节点
            this.rootNode = newRoot;
            
            // 重新渲染思维导图
            await this.renderMindMap();
            
            // 显示同步状态
            this.showSyncStatus('已从源文件更新', 'success');
            
            this.logger.info('从源文件更新了思维导图');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.showSyncStatus(`更新失败: ${errorMessage}`, 'error');
            this.logger.error('从源文件更新失败', error);
        } finally {
            this.isSyncing = false;
        }
    }

    // 保持节点状态（展开/折叠、选中状态等）
    private preserveNodeStates(oldRoot: MindMapNode, newRoot: MindMapNode) {
        // 构建旧节点映射
        const oldNodeMap = new Map<string, MindMapNode>();
        this.buildNodeMap(oldRoot, oldNodeMap);
        
        // 应用节点状态
        this.applyNodeStates(newRoot, oldNodeMap);
    }

    // 构建节点映射（基于内容）
    private buildNodeMap(node: MindMapNode, map: Map<string, MindMapNode>) {
        map.set(node.content, node);
        for (const child of node.children) {
            this.buildNodeMap(child, map);
        }
    }

    // 应用节点状态
    private applyNodeStates(node: MindMapNode, oldNodeMap: Map<string, MindMapNode>) {
        const oldNode = oldNodeMap.get(node.content);
        if (oldNode) {
            node.isExpanded = oldNode.isExpanded;
            node.isSelected = oldNode.isSelected;
        }
        
        for (const child of node.children) {
            this.applyNodeStates(child, oldNodeMap);
        }
    }

    // 同步到源文件（带防抖）
    syncToSourceFile(filePath: string, immediate: boolean = false) {
        if (this.isSyncing || !this.rootNode) return;
        
        // 清除之前的超时
        if (this.syncTimeouts[filePath]) {
            clearTimeout(this.syncTimeouts[filePath]);
        }
        
        // 设置新的超时
        const delay = immediate ? 0 : this.settings.autoSyncInterval;
        this.syncTimeouts[filePath] = setTimeout(() => {
            this.performSync(filePath);
        }, delay);
    }

    // 执行实际的同步操作
    private async performSync(filePath: string) {
        if (this.isSyncing || !this.rootNode) return;
        
        try {
            this.isSyncing = true;
            
            // 获取文件
            const file = this.app.vault.getAbstractFileByPath(filePath);
            if (!(file instanceof TFile)) {
                throw new Error('找不到源文件');
            }
            
            // 生成Markdown内容
            const markdown = this.markdownConverter.generateCleanMarkdownFromMindMap(this.rootNode);
            
            // 更新文件
            await this.app.vault.modify(file, markdown);
            
            // 显示同步状态
            this.showSyncStatus('已同步到源文件', 'success');
            
            this.logger.info('已同步到源文件');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.showSyncStatus(`同步失败: ${errorMessage}`, 'error');
            this.logger.error('同步到源文件失败', error);
        } finally {
            this.isSyncing = false;
        }
    }

    // 显示同步状态
    private showSyncStatus(message: string, type: 'success' | 'error' | 'info' = 'info') {
        const view = this.getMindMapView();
        if (!view) return;
        
        const statusEl = view.containerEl.querySelector('.mindmap-sync-status');
        if (!statusEl) return;
        
        // 设置状态类
        statusEl.removeClass('success', 'error', 'info');
        statusEl.addClass(type);
        
        // 设置消息
        statusEl.textContent = message;
        
        // 显示状态
        statusEl.addClass('visible');
        
        // 3秒后隐藏
        setTimeout(() => {
            statusEl.removeClass('visible');
        }, 3000);
    }

    // 渲染思维导图
    async renderMindMap() {
        try {
            if (!this.rootNode) {
                this.logger.warn('没有根节点数据，无法渲染');
                return;
            }

            const view = this.getMindMapView();
            if (!view) {
                this.logger.warn('没有找到思维导图视图');
                return;
            }

            const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
            if (!container) {
                this.logger.error('没有找到思维导图容器');
                return;
            }

            // 清空容器
            container.empty();

            // 根据设置选择渲染模式
            if (this.settings.defaultViewMode === 'markmap') {
                await this.renderMarkmap(container);
            } else {
                this.renderSimpleHTML(container);
            }

            // 高亮选中的节点
            this.highlightSelectedNode();

            this.logger.debug('思维导图渲染完成');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.logger.error('渲染思维导图时出错', error);
            new Notice(`渲染失败: ${errorMessage}`);
        }
    }

    // 渲染Markmap
    private async renderMarkmap(container: HTMLElement) {
        if (!this.rootNode) return;
        
        try {
            // 创建SVG元素
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.width = '100%';
            svg.style.height = '100%';
            container.appendChild(svg);
            
            // 转换数据格式
            const { root: markmapData } = this.transformer.transform(
                this.markdownConverter.generateMarkdownFromMindMap(this.rootNode)
            );
            
            // 添加自定义ID
            this.addCustomNodeIds(markmapData, this.rootNode);
            
            // 创建Markmap
            this.mindmap = Markmap.create(svg, {
                autoFit: true,
                color: (node: any) => {
                    // 根据节点类型设置颜色
                    const nodeId = node.data?.id;
                    if (!nodeId) return '#1E88E5';
                    
                    const mindmapNode = findNodeById(this.rootNode!, nodeId);
                    if (!mindmapNode) return '#1E88E5';
                    
                    if (mindmapNode.isSelected) {
                        return '#F44336'; // 选中节点为红色
                    }
                    
                    switch (mindmapNode.nodeType) {
                        case 'heading': return '#1E88E5'; // 标题为蓝色
                        case 'list': return '#43A047'; // 列表为绿色
                        case 'paragraph': return '#FB8C00'; // 段落为橙色
                        case 'root': return '#9C27B0'; // 根节点为紫色
                        default: return '#1E88E5';
                    }
                }
            }, markmapData);
            
            // 设置事件监听器
            this.setupEventListeners(svg);
            
            this.logger.debug('Markmap渲染完成');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.logger.error('Markmap渲染失败', error);

            // 回退到简单HTML渲染
            container.empty();
            this.renderSimpleHTML(container);

            new Notice(`Markmap渲染失败，已回退到简单模式: ${errorMessage}`);
        }
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        // 添加ID
        if (!markmapNode.data) markmapNode.data = {};
        markmapNode.data.id = mindmapNode.id;
        
        // 处理子节点
        if (markmapNode.children && mindmapNode.children) {
            const minLength = Math.min(markmapNode.children.length, mindmapNode.children.length);
            for (let i = 0; i < minLength; i++) {
                this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
            }
        }
    }

    // 渲染简单HTML思维导图
    private renderSimpleHTML(container: HTMLElement) {
        if (!this.rootNode) return;
        
        container.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;
        
        // 创建根节点
        const rootElement = this.createNodeElement(this.rootNode, 0);
        container.appendChild(rootElement);
        
        // 创建子节点
        const childrenContainer = container.createDiv('children-container');
        childrenContainer.style.cssText = `
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `;
        
        this.rootNode.children.forEach(child => {
            const childElement = this.createNodeElement(child, 1);
            childrenContainer.appendChild(childElement);
        });
        
        this.logger.debug('简单HTML渲染完成');
    }

    // 创建节点元素
    private createNodeElement(node: MindMapNode, level: number): HTMLElement {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'simple-mindmap-node';
        nodeEl.setAttribute('data-node-id', node.id);
        nodeEl.setAttribute('tabindex', '0'); // 使元素可以获得焦点
        
        const isSelected = this.selectedNode === node;
        
        nodeEl.style.cssText = `
            padding: 10px 15px;
            border: 2px solid ${isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)'};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${level === 0 ? '600' : '400'};
            font-size: ${level === 0 ? '16px' : '14px'};
            color: ${isSelected ? 'var(--text-accent)' : 'var(--text-normal)'};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `;
        
        nodeEl.textContent = node.content;
        
        // 添加事件监听器
        nodeEl.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectNode(node);
            this.updateSimpleNodeStyles();
        });
        
        nodeEl.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            this.startSimpleEditing(node, nodeEl);
        });
        
        // 添加键盘事件支持
        nodeEl.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.selectedNode === node) {
                e.preventDefault();
                this.startSimpleEditing(node, nodeEl);
            }
        });
        
        return nodeEl;
    }

    // 更新简单节点样式
    private updateSimpleNodeStyles() {
        const nodes = document.querySelectorAll('.simple-mindmap-node');
        nodes.forEach(nodeEl => {
            const nodeId = nodeEl.getAttribute('data-node-id');
            const isSelected = this.selectedNode?.id === nodeId;
            
            (nodeEl as HTMLElement).style.borderColor = isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)';
            (nodeEl as HTMLElement).style.color = isSelected ? 'var(--text-accent)' : 'var(--text-normal)';
        });
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 点击事件
        container.addEventListener('click', (evt) => {
            this.handleSVGClick(evt as MouseEvent);
        });
        
        // 双击事件
        container.addEventListener('dblclick', (evt) => {
            this.handleSVGDoubleClick(evt as MouseEvent);
        });
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        // 查找点击的文本元素
        let target = evt.target as Element;
        while (target && target.tagName !== 'text' && target.tagName !== 'tspan') {
            target = target.parentElement as Element;
            if (!target || target.tagName === 'svg') break;
        }
        
        if (!target || (target.tagName !== 'text' && target.tagName !== 'tspan')) return;
        
        // 查找节点ID
        const nodeId = this.findNodeIdFromElement(target);
        if (!nodeId || !this.rootNode) return;
        
        // 查找节点
        const node = findNodeById(this.rootNode, nodeId);
        if (!node) return;
        
        // 选中节点
        this.selectNode(node);
        
        // 重新渲染以更新颜色
        this.renderMindMap().catch(console.error);
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        // 查找点击的文本元素
        let target = evt.target as Element;
        while (target && target.tagName !== 'text' && target.tagName !== 'tspan') {
            target = target.parentElement as Element;
            if (!target || target.tagName === 'svg') break;
        }
        
        if (!target || (target.tagName !== 'text' && target.tagName !== 'tspan')) return;
        
        // 查找节点ID
        const nodeId = this.findNodeIdFromElement(target);
        if (!nodeId || !this.rootNode) return;
        
        // 查找节点
        const node = findNodeById(this.rootNode, nodeId);
        if (!node) return;
        
        // 开始编辑
        this.startEditing(node).catch(console.error);
    }

    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // 查找最近的g元素
        let gElement = element;
        while (gElement && gElement.tagName !== 'g') {
            gElement = gElement.parentElement as Element;
            if (!gElement) return null;
        }
        
        // 查找data-id属性
        const dataId = gElement.getAttribute('data-id');
        if (dataId) return dataId;
        
        // 如果没有data-id，尝试通过内容匹配
        const content = element.textContent;
        if (!content || !this.rootNode) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = getAllNodes(this.rootNode);
        for (const node of allNodes) {
            if (node.content === content) {
                return node.id;
            }
        }
        
        return null;
    }

    // 处理节点选中
    public selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }
        
        this.selectedNode = node;
        if (node) {
            node.isSelected = true;
            
            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode) return;
        
        // 查找节点元素
        const nodeEl = document.querySelector(`[data-node-id="${this.selectedNode.id}"]`);
        if (nodeEl) {
            // 简单HTML模式
            (nodeEl as HTMLElement).style.borderColor = 'var(--text-accent)';
            (nodeEl as HTMLElement).style.color = 'var(--text-accent)';
            
            // 确保节点可见
            nodeEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else if (this.mindmap) {
            // Markmap模式，通过重新渲染更新颜色
            // 颜色在renderMarkmap中通过color函数设置
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private async expandNode(node: MindMapNode) {
        node.isExpanded = true;
        await this.renderMindMap();
    }

    // 折叠节点
    private async collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        await this.renderMindMap();
    }

    // 创建子节点
    createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: generateUniqueId(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true,
            nodeType: 'paragraph'
        };
        
        parentNode.children.push(newNode);
        
        // 选中新节点
        this.selectNode(newNode);
        
        // 重新渲染
        this.renderMindMap().catch(console.error);
        
        // 开始编辑新节点
        setTimeout(() => {
            this.startEditing(newNode).catch(console.error);
        }, 100);
        
        // 保存数据
        this.saveMindMapData();
    }

    // 创建同级节点
    createSiblingNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const parent = node.parent;
        const newNode: MindMapNode = {
            id: generateUniqueId(),
            content: '新节点',
            children: [],
            parent: parent,
            isExpanded: true,
            nodeType: node.nodeType
        };
        
        // 在当前节点后插入
        const index = parent.children.indexOf(node);
        parent.children.splice(index + 1, 0, newNode);
        
        // 选中新节点
        this.selectNode(newNode);
        
        // 重新渲染
        this.renderMindMap().catch(console.error);
        
        // 开始编辑新节点
        setTimeout(() => {
            this.startEditing(newNode).catch(console.error);
        }, 100);
        
        // 保存数据
        this.saveMindMapData();
    }

    // 删除节点
    deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) {
            new Notice('无法删除根节点');
            return;
        }

        const parent = node.parent;
        const siblings = parent.children;
        const index = siblings.indexOf(node);

        if (index === -1) {
            this.logger.warn('要删除的节点不在父节点的子节点列表中');
            return;
        }

        // 移除节点
        siblings.splice(index, 1);

        // 智能选择下一个节点
        this.selectNextNodeAfterDeletion(siblings, index, parent);

        // 重新渲染
        this.renderMindMap().catch(console.error);

        // 保存数据
        this.saveMindMapData();

        this.logger.info(`已删除节点: ${node.content}`);
    }

    // 删除节点后的智能选择
    private selectNextNodeAfterDeletion(siblings: MindMapNode[], deletedIndex: number, parent: MindMapNode) {
        if (siblings.length > 0) {
            // 优先选择下一个同级节点，如果没有则选择上一个
            const nextIndex = Math.min(deletedIndex, siblings.length - 1);
            this.selectNode(siblings[nextIndex]);
        } else {
            // 如果没有同级节点，选择父节点
            this.selectNode(parent);
        }
    }

    // 开始编辑节点
    async startEditing(node: MindMapNode) {
        if (!node) {
            this.logger.warn('尝试编辑空节点');
            return;
        }

        this.logger.debug('开始编辑节点:', node.content);

        if (this.editingNode === node) {
            this.logger.debug('已经在编辑此节点');
            return;
        }

        // 完成当前编辑
        await this.finishEditing();

        // 设置新的编辑节点
        this.editingNode = node;
        
        // 检查是否在简单HTML模式
        const simpleNode = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement;
        if (simpleNode) {
            this.logger.debug('使用简单HTML编辑');
            this.startSimpleEditing(node, simpleNode);
            return;
        }
        
        // markmap模式的编辑
        this.logger.debug('使用markmap编辑模式');
        this.startMarkmapEditing(node);
    }

    // markmap模式的编辑
    private startMarkmapEditing(node: MindMapNode) {
        this.logger.debug('开始markmap编辑模式');
        
        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) {
            this.logger.warn('未找到SVG元素，无法编辑');
            return;
        }
        
        // 查找所有文本元素
        const textElements = Array.from(svg.querySelectorAll('text, tspan'));
        
        // 查找匹配的元素
        let targetElement: Element | null = null;
        for (const el of textElements) {
            if (el.textContent === node.content) {
                targetElement = el;
                break;
            }
        }
        
        // 如果没有找到匹配的元素，尝试回退到简单编辑模式
        if (!targetElement) {
            this.logger.warn('未找到目标元素，回退到简单编辑模式');
            this.fallbackToSimpleEditing(node);
            return;
        }
        
        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();
        
        // 创建输入框
        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';
        
        // 设置输入框样式
        Object.assign(input.style, {
            position: 'fixed',
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            width: `${Math.max(rect.width + 40, 100)}px`,
            height: `${Math.max(rect.height + 8, 30)}px`,
            zIndex: '10000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)',
            background: 'var(--background-primary)',
            border: '2px solid var(--text-accent)',
            borderRadius: '4px',
            padding: '4px 8px',
            color: 'var(--text-normal)',
            outline: 'none',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
        });
        
        // 将输入框添加到body
        document.body.appendChild(input);
        
        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);
        
        // 添加事件监听器
        input.addEventListener('blur', () => {
            this.finishEditing().catch(console.error);
        });
        
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.finishEditing().catch(console.error);
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });
    }

    // 回退到简单HTML编辑模式
    private fallbackToSimpleEditing(node: MindMapNode) {
        // 创建临时节点元素
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'fixed';
        tempContainer.style.left = '-9999px';
        document.body.appendChild(tempContainer);
        
        const tempNodeEl = this.createNodeElement(node, 0);
        tempContainer.appendChild(tempNodeEl);
        
        // 开始简单编辑
        this.startSimpleEditing(node, tempNodeEl);
        
        // 编辑完成后移除临时容器
        const observer = new MutationObserver(() => {
            if (!tempContainer.contains(tempNodeEl)) {
                tempContainer.remove();
                observer.disconnect();
            }
        });
        
        observer.observe(tempContainer, { childList: true });
    }

    // 简单编辑功能
    private startSimpleEditing(node: MindMapNode, nodeEl: HTMLElement) {
        const input = document.createElement('input');
        input.type = 'text';
        input.value = node.content;
        input.className = 'simple-mindmap-input';
        
        // 复制原有样式并添加编辑样式
        input.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${node === this.rootNode ? '600' : '400'};
            font-size: ${node === this.rootNode ? '16px' : '14px'};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `;
        
        nodeEl.parentNode?.replaceChild(input, nodeEl);
        
        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);
        
        // 设置当前编辑节点
        this.editingNode = node;
        
        const finishEdit = () => {
            if (this.editingNode === node) {
                const newContent = input.value.trim();
                if (newContent) {
                    node.content = newContent;
                    this.logger.debug(`节点内容已更新: ${newContent}`);
                } else {
                    this.logger.warn('节点内容不能为空，保持原内容');
                    new Notice('节点内容不能为空');
                }

                this.editingNode = null;

                // 重新渲染
                this.renderMindMap().catch(console.error);

                // 保存数据
                this.saveMindMapData();
            }
        };
        
        const cancelEdit = () => {
            this.editingNode = null;
            this.renderMindMap().catch(console.error);
        };
        
        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 完成编辑
    async finishEditing() {
        if (!this.editingNode) return;

        // 查找输入框
        const input = document.querySelector('.mindmap-node-input, .simple-mindmap-input') as HTMLInputElement;
        if (input) {
            const newContent = input.value.trim();

            // 验证内容
            if (newContent) {
                this.editingNode.content = newContent;
                this.logger.debug(`节点编辑完成: ${newContent}`);
            } else {
                this.logger.warn('编辑内容为空，保持原内容');
                new Notice('节点内容不能为空');
            }

            // 移除输入框
            input.remove();
        }

        const editingNode = this.editingNode;
        this.editingNode = null;

        // 重新渲染
        await this.renderMindMap();

        // 保存数据
        await this.saveMindMapData();

        return editingNode;
    }

    // 取消编辑
    cancelEditing() {
        // 查找输入框
        const input = document.querySelector('.mindmap-node-input, .simple-mindmap-input') as HTMLInputElement;
        if (input) {
            input.remove();
        }
        
        this.editingNode = null;
        
        // 重新渲染
        this.renderMindMap().catch(console.error);
    }

    // 保存数据并同步到源文件
    async saveMindMapData() {
        try {
            if (!this.rootNode) {
                this.logger.warn('没有根节点数据，无法保存');
                return;
            }

            // 获取当前视图
            const view = this.getMindMapView();
            if (!view) {
                this.logger.warn('没有找到思维导图视图，无法保存状态');
                return;
            }

            // 更新视图状态
            const state = view.getState();
            state.data = this.rootNode;
            view.setState(state, { history: false });

            // 如果有源文件且启用了实时同步，则同步到源文件
            const sourceFile = state.sourceFile as string;
            if (sourceFile && this.settings.realTimeSync) {
                this.syncToSourceFile(sourceFile);
            }

            this.logger.debug('数据保存完成');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.logger.error('保存数据时出错', error);
            new Notice(`保存失败: ${errorMessage}`);
        }
    }

    // 关闭思维导图视图
    public closeMindMapView(leaf: WorkspaceLeaf) {
        // 如果有源文件，确保同步
        const view = leaf.view as MindMapView;
        if (view) {
            const state = view.getState();
            const sourceFile = state.sourceFile as string;
            
            if (sourceFile && this.settings.realTimeSync && this.rootNode) {
                this.syncToSourceFile(sourceFile, true);
            }
        }
    }

    // 加载思维导图数据
    public async loadMindMapData(data?: MindMapNode) {
        if (!data) return;

        try {
            // 克隆数据以避免引用问题
            this.rootNode = JSON.parse(JSON.stringify(data));

            // 重建父节点引用
            if (this.rootNode) {
                reconstructNodeTree(this.rootNode);
            }

            // 渲染思维导图
            await this.renderMindMap();

            this.logger.debug('思维导图数据加载完成');
        } catch (error) {
            const errorMessage = this.getErrorMessage(error);
            this.logger.error('加载思维导图数据失败', error);
            new Notice(`数据加载失败: ${errorMessage}`);
        }
    }

    // 获取根节点数据
    public getRootNode(): MindMapNode | null {
        return this.rootNode;
    }
}

/**
 * 思维导图视图类
 */
export class MindMapView extends ItemView {
    private plugin: MindMapPlugin;
    
    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }
    
    getViewType(): string {
        return MIND_MAP_VIEW_TYPE;
    }
    
    getDisplayText(): string {
        return '思维导图';
    }
    
    async onOpen() {
        try {
            // 创建容器
            const container = this.containerEl.createDiv('mindmap-container');
            container.style.cssText = `
                width: 100%;
                height: 100%;
                overflow: auto;
                position: relative;
            `;

            // 添加同步状态指示器
            this.addSyncStatusIndicator(this.containerEl);

            // 加载数据
            const state = this.getState();
            if (state.data) {
                await this.plugin.loadMindMapData(state.data);

                // 如果有选中的节点ID，恢复选中状态
                if (state.selectedNodeId && this.plugin.rootNode) {
                    const selectedNode = findNodeById(this.plugin.rootNode, state.selectedNodeId);
                    if (selectedNode) {
                        this.plugin.selectNode(selectedNode);
                    }
                }

                // 如果有源文件，设置文件监听器
                if (state.sourceFile) {
                    this.setupFileWatcher(state.sourceFile as string);
                }
            } else {
                // 创建默认数据
                await this.plugin.createNewMindMap();
            }
        } catch (error) {
            const errorMessage = this.plugin.getErrorMessage(error);
            this.plugin.logger.error('打开思维导图视图时出错', error);
            new Notice(`视图初始化失败: ${errorMessage}`);
        }
    }
    
    async onClose() {
        // 在视图关闭时同步数据
        this.plugin.closeMindMapView(this.leaf);
    }
    
    getState(): MindMapViewState {
        const state = super.getState() as MindMapViewState;
        
        // 保存当前数据
        if (this.plugin.rootNode) {
            state.data = this.plugin.rootNode;
        }
        
        // 保存选中节点ID
        if (this.plugin.selectedNode) {
            state.selectedNodeId = this.plugin.selectedNode.id;
        }
        
        return state;
    }
    
    async setState(state: Record<string, unknown>, result: ViewStateResult): Promise<void> {
        await super.setState(state, result);
        
        // 更新数据
        if (state.data) {
            await this.plugin.loadMindMapData(state.data as MindMapNode);
        }
    }
    
    // 添加同步状态指示器到视图
    private addSyncStatusIndicator(container: HTMLElement) {
        const statusEl = container.createDiv('mindmap-sync-status');
        statusEl.style.cssText = `
            position: absolute;
            bottom: 10px;
            right: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        `;
        
        // 添加CSS类
        statusEl.addClass('mindmap-sync-status');
    }
    
    // 设置文件监听器
    private setupFileWatcher(filePath: string) {
        // 注册文件修改事件
        this.registerEvent(
            this.app.vault.on('modify', (file) => {
                if (file.path === filePath && !this.plugin.isSyncing) {
                    this.plugin.updateFromSourceFile(file as TFile);
                }
            })
        );
    }
}
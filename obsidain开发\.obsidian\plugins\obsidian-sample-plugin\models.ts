/**
 * 思维导图节点接口
 * 定义思维导图中节点的数据结构
 */
export interface MindMapNode {
    id: string;
    content: string;
    children: MindMapNode[];
    parent?: MindMapNode;
    isSelected?: boolean;
    isExpanded?: boolean;
    isHeading?: boolean; // 标识是否为原始标题节点
    originalLevel?: number; // 原始标题级别
    nodeType?: 'heading' | 'paragraph' | 'list' | 'root'; // 节点类型
}

/**
 * 思维导图视图状态接口
 * 用于保存和恢复视图状态
 */
export interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;
    selectedNodeId?: string;
    sourceFile?: string; // 源文件路径
}

/**
 * 同步状态类型
 * 用于表示同步操作的状态
 */
export type SyncStatusType = 'success' | 'error' | 'info';

/**
 * 思维导图视图类型常量
 */
export const MIND_MAP_VIEW_TYPE = "mindmap";

/**
 * 生成唯一ID
 * 用于为思维导图节点创建唯一标识符
 * @returns 唯一ID字符串
 */
export function generateUniqueId(): string {
    return 'node_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
}

/**
 * 深度克隆思维导图节点
 * 创建节点的深拷贝，但不包括父节点引用（避免循环引用）
 * @param node 要克隆的节点
 * @returns 克隆后的节点
 */
export function cloneNodeWithoutParent(node: MindMapNode): MindMapNode {
    const clone: MindMapNode = {
        id: node.id,
        content: node.content,
        children: [],
        isSelected: node.isSelected,
        isExpanded: node.isExpanded,
        isHeading: node.isHeading,
        originalLevel: node.originalLevel,
        nodeType: node.nodeType
    };
    
    for (const child of node.children) {
        const childClone = cloneNodeWithoutParent(child);
        clone.children.push(childClone);
    }
    
    return clone;
}

/**
 * 重建节点树的父节点引用
 * 在节点树中设置每个节点的父节点引用
 * @param node 当前节点
 * @param parent 父节点
 */
export function reconstructNodeTree(node: MindMapNode, parent: MindMapNode | undefined = undefined): void {
    node.parent = parent;
    
    for (const child of node.children) {
        reconstructNodeTree(child, node);
    }
}

/**
 * 查找节点
 * 根据ID在节点树中查找特定节点
 * @param root 根节点
 * @param id 要查找的节点ID
 * @returns 找到的节点或undefined
 */
export function findNodeById(root: MindMapNode, id: string): MindMapNode | undefined {
    if (root.id === id) {
        return root;
    }
    
    for (const child of root.children) {
        const found = findNodeById(child, id);
        if (found) {
            return found;
        }
    }
    
    return undefined;
}

/**
 * 获取所有节点
 * 以数组形式返回节点树中的所有节点
 * @param root 根节点
 * @returns 所有节点的数组
 */
export function getAllNodes(root: MindMapNode): MindMapNode[] {
    const nodes: MindMapNode[] = [root];
    for (const child of root.children) {
        nodes.push(...getAllNodes(child));
    }
    return nodes;
}
/**
 * 思维导图视图组件
 * 自定义Obsidian视图，集成思维导图渲染器
 */

import { ItemView, WorkspaceLeaf, MarkdownView } from 'obsidian';
import { MindMapRenderer } from '../core/MindMapRenderer';
import { MarkdownParser } from '../core/MarkdownParser';
import { Logger } from '../utils';
import MindMapSyncPlugin from '../../main';

export const MINDMAP_VIEW_TYPE = 'mindmap-view';

export class MindMapView extends ItemView {
  private plugin: MindMapSyncPlugin;
  private renderer: MindMapRenderer | null = null;
  private parser: MarkdownParser;
  private logger: Logger;
  private currentFile: string | null = null;
  private isUpdating = false;
  
  constructor(leaf: WorkspaceLeaf, plugin: MindMapSyncPlugin) {
    super(leaf);
    this.plugin = plugin;
    this.logger = new Logger(plugin.settings.debugMode);
    this.parser = new MarkdownParser(this.logger);
  }
  
  getViewType(): string {
    return MINDMAP_VIEW_TYPE;
  }
  
  getDisplayText(): string {
    return '思维导图';
  }
  
  getIcon(): string {
    return 'git-branch';
  }
  
  async onOpen(): Promise<void> {
    this.logger.debug('思维导图视图打开');
    
    // 创建容器
    const container = this.containerEl.children[1] as HTMLElement;
    container.empty();
    container.addClass('mindmap-view-container');

    // 创建工具栏
    this.createToolbar(container);
    
    // 创建思维导图容器
    const mindmapContainer = container.createDiv('mindmap-container');
    mindmapContainer.style.height = 'calc(100% - 40px)';
    mindmapContainer.style.width = '100%';
    
    // 初始化渲染器
    this.renderer = new MindMapRenderer(mindmapContainer, this.logger);
    
    // 设置节点变更回调
    this.renderer.setNodeChangeCallback((nodeId, changes) => {
      this.handleNodeChange(nodeId, changes);
    });
    
    // 监听活动文件变化
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        this.updateFromActiveFile();
      })
    );
    
    // 监听文件内容变化
    this.registerEvent(
      this.app.workspace.on('editor-change', (editor, info) => {
        if (!this.isUpdating && this.plugin.settings.enableSync) {
          this.debounceUpdate();
        }
      })
    );
    
    // 初始加载
    this.updateFromActiveFile();
  }
  
  async onClose(): Promise<void> {
    this.logger.debug('思维导图视图关闭');
    
    if (this.renderer) {
      this.renderer.destroy();
      this.renderer = null;
    }
  }
  
  /**
   * 创建工具栏
   */
  private createToolbar(container: HTMLElement): void {
    const toolbar = container.createDiv('mindmap-toolbar');
    toolbar.style.height = '40px';
    toolbar.style.padding = '8px';
    toolbar.style.borderBottom = '1px solid var(--background-modifier-border)';
    toolbar.style.display = 'flex';
    toolbar.style.alignItems = 'center';
    toolbar.style.gap = '8px';
    
    // 刷新按钮
    const refreshBtn = toolbar.createEl('button', {
      text: '刷新',
      cls: 'mod-cta'
    });
    refreshBtn.addEventListener('click', () => {
      this.updateFromActiveFile();
    });
    
    // 同步开关
    const syncToggle = toolbar.createEl('button', {
      text: this.plugin.settings.enableSync ? '同步: 开' : '同步: 关',
      cls: this.plugin.settings.enableSync ? 'mod-cta' : ''
    });
    syncToggle.addEventListener('click', () => {
      this.plugin.settings.enableSync = !this.plugin.settings.enableSync;
      syncToggle.textContent = this.plugin.settings.enableSync ? '同步: 开' : '同步: 关';
      syncToggle.className = this.plugin.settings.enableSync ? 'mod-cta' : '';
      this.plugin.saveSettings();
    });
    
    // 居中按钮
    const centerBtn = toolbar.createEl('button', {
      text: '居中'
    });
    centerBtn.addEventListener('click', () => {
      if (this.renderer) {
        this.renderer.fit();
      }
    });
    
    // 文件名显示
    const fileLabel = toolbar.createEl('span', {
      cls: 'mindmap-file-label'
    });
    fileLabel.style.marginLeft = 'auto';
    fileLabel.style.fontSize = '12px';
    fileLabel.style.color = 'var(--text-muted)';
    
    this.updateFileLabel(fileLabel);
  }
  
  /**
   * 更新文件标签
   */
  private updateFileLabel(label: HTMLElement): void {
    const activeFile = this.app.workspace.getActiveFile();
    if (activeFile) {
      label.textContent = activeFile.basename;
      this.currentFile = activeFile.path;
    } else {
      label.textContent = '无活动文件';
      this.currentFile = null;
    }
  }
  
  /**
   * 从活动文件更新思维导图
   */
  private async updateFromActiveFile(): Promise<void> {
    const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
    if (!activeView || !this.renderer) {
      this.logger.debug('无活动Markdown视图或渲染器未初始化');
      return;
    }
    
    const file = activeView.file;
    if (!file) {
      this.logger.debug('无活动文件');
      return;
    }
    
    try {
      this.isUpdating = true;
      
      // 读取文件内容
      const content = await this.app.vault.read(file);
      
      // 解析并渲染
      const nodes = this.parser.parse(content);
      const markdown = this.parser.toMarkdown(nodes);
      
      this.renderer.render(markdown);
      
      this.currentFile = file.path;
      this.logger.debug(`思维导图已更新: ${file.basename}`);
      
      // 更新文件标签
      const fileLabel = this.containerEl.querySelector('.mindmap-file-label') as HTMLElement;
      if (fileLabel) {
        this.updateFileLabel(fileLabel);
      }
      
    } catch (error) {
      this.logger.error('更新思维导图失败', error);
    } finally {
      this.isUpdating = false;
    }
  }
  
  /**
   * 防抖更新
   */
  private debounceUpdate = this.debounce(() => {
    this.updateFromActiveFile();
  }, 300); // 使用默认值，避免初始化问题
  
  /**
   * 防抖函数
   */
  private debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }
  
  /**
   * 处理节点变更
   */
  private async handleNodeChange(nodeId: string, changes: any): Promise<void> {
    if (!this.plugin.settings.enableSync || this.isUpdating) {
      return;
    }
    
    this.logger.debug('处理节点变更', { nodeId, changes });
    
    try {
      this.isUpdating = true;
      
      // 获取当前活动的Markdown视图
      const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
      if (!activeView) {
        this.logger.warn('无活动Markdown视图，无法同步变更');
        return;
      }
      
      // 这里需要实现思维导图到Markdown的同步逻辑
      // 暂时只记录日志
      this.logger.debug('节点变更已记录，等待同步实现');
      
    } catch (error) {
      this.logger.error('处理节点变更失败', error);
    } finally {
      this.isUpdating = false;
    }
  }
  
  /**
   * 手动刷新思维导图
   */
  public async refresh(): Promise<void> {
    await this.updateFromActiveFile();
  }
  
  /**
   * 获取当前渲染器
   */
  public getRenderer(): MindMapRenderer | null {
    return this.renderer;
  }
  
  /**
   * 获取当前解析器
   */
  public getParser(): MarkdownParser {
    return this.parser;
  }
}

# 智能思维导图插件测试文档

这是一个用于测试智能思维导图插件的示例文档。

## 主要功能

### 1. Markdown解析
- 支持标题层级解析
- 支持列表项解析
- 支持代码块识别

### 2. 思维导图渲染
- 基于D3.js的可视化
- 支持节点交互
- 支持缩放和平移

### 3. 双向同步
- Markdown到思维导图
- 思维导图到Markdown
- 实时同步更新

## 技术架构

### 前端技术
- TypeScript
- D3.js
- Obsidian API

### 核心模块
- MarkdownParser: 文档解析
- MindMapRenderer: 思维导图渲染
- SyncManager: 同步管理
- AIManager: AI服务集成

## 使用说明

1. 安装插件
2. 打开思维导图视图
3. 编辑Markdown文档
4. 观察思维导图变化

## 测试用例

### 简单列表
- 项目1
- 项目2
  - 子项目2.1
  - 子项目2.2
- 项目3

### 代码示例

```javascript
function hello() {
  console.log("Hello, MindMap!");
}
```

### 多级标题

#### 四级标题
内容示例

##### 五级标题
更深层的内容

###### 六级标题
最深层的内容

## 总结

这个插件实现了Markdown文档与思维导图的双向同步，为知识管理提供了新的可视化方式。

# 思维导图双击编辑功能

本文档描述了思维导图插件中双击编辑功能的实现和使用方法。

## 功能概述

思维导图插件支持通过双击节点来编辑节点内容。这个功能在两种渲染模式下都可用：

1. **Markmap模式**：使用markmap库渲染的思维导图
2. **简单HTML模式**：使用HTML元素渲染的思维导图

## 使用方法

1. 打开一个Markdown文件
2. 使用命令面板执行"从Markdown文档导入"命令，创建思维导图视图
3. 双击任意节点开始编辑
4. 编辑完成后，按回车键保存修改，或按Esc键取消修改

## 技术实现

### 事件处理

双击编辑功能通过以下事件处理机制实现：

1. 在思维导图容器上设置事件监听器，捕获双击事件
2. 为所有文本元素和节点组单独添加双击事件监听器
3. 使用MutationObserver监听动态添加的元素，确保新添加的节点也能响应双击事件

### 节点识别

当用户双击节点时，插件通过以下步骤识别被点击的节点：

1. 从事件目标元素提取文本内容
2. 在思维导图数据结构中查找匹配的节点
3. 如果找不到精确匹配，尝试部分匹配和宽松匹配

### 编辑功能

编辑功能通过以下步骤实现：

1. 创建输入框或文本区域，定位到被点击的节点位置
2. 将节点内容填充到输入框中
3. 当用户完成编辑后，更新节点内容并重新渲染思维导图
4. 将更改同步到源Markdown文件

## 注意事项

1. 编辑多行内容时，会使用文本区域而不是单行输入框
2. 编辑完成后，思维导图会重新渲染，可能导致节点位置变化
3. 如果markmap模式下无法正确编辑，插件会自动切换到简单HTML模式

## 故障排除

如果双击编辑功能不工作，可以尝试以下步骤：

1. 检查控制台是否有错误信息
2. 尝试使用命令面板执行"强制使用简单HTML视图"命令
3. 重新加载插件
4. 重启Obsidian

## 已知问题

1. 在某些情况下，编辑框可能无法精确定位到节点位置
2. 编辑包含特殊格式的节点可能导致格式丢失
3. 在高缩放级别下，编辑框位置可能不准确
/**
 * 工具函数集合
 */

import { NodeData, Position } from '../types';

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as any;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as any;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 解析Markdown标题层级
 */
export function parseHeadingLevel(line: string): number {
  const match = line.match(/^(#{1,6})\s/);
  return match ? match[1].length : 0;
}

/**
 * 解析列表层级
 */
export function parseListLevel(line: string): number {
  const match = line.match(/^(\s*)([-*+]|\d+\.)\s/);
  return match ? Math.floor(match[1].length / 2) + 1 : 0;
}

/**
 * 检查是否为代码块
 */
export function isCodeBlock(line: string): boolean {
  return line.trim().startsWith('```');
}

/**
 * 检查是否为链接
 */
export function isLink(text: string): boolean {
  return /\[([^\]]+)\]\(([^)]+)\)/.test(text);
}

/**
 * 提取链接信息
 */
export function extractLinkInfo(text: string): { text: string; url: string } | null {
  const match = text.match(/\[([^\]]+)\]\(([^)]+)\)/);
  return match ? { text: match[1], url: match[2] } : null;
}

/**
 * 清理Markdown格式
 */
export function cleanMarkdown(text: string): string {
  return text
    .replace(/^#{1,6}\s+/, '') // 移除标题标记
    .replace(/^\s*[-*+]\s+/, '') // 移除列表标记
    .replace(/^\s*\d+\.\s+/, '') // 移除有序列表标记
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
    .replace(/`(.*?)`/g, '$1') // 移除行内代码标记
    .trim();
}

/**
 * 计算文本相似度
 */
export function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) {
    return 1.0;
  }
  
  const distance = levenshteinDistance(longer, shorter);
  return (longer.length - distance) / longer.length;
}

/**
 * 计算编辑距离
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * 格式化时间
 */
export function formatTime(date: Date): string {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

/**
 * 日志记录器
 */
export class Logger {
  private debugMode: boolean;
  
  constructor(debugMode: boolean = false) {
    this.debugMode = debugMode;
  }
  
  setDebugMode(enabled: boolean) {
    this.debugMode = enabled;
  }
  
  debug(message: string, ...args: any[]) {
    if (this.debugMode) {
      console.log(`[MindMapSync Debug] ${message}`, ...args);
    }
  }
  
  info(message: string, ...args: any[]) {
    console.log(`[MindMapSync] ${message}`, ...args);
  }
  
  warn(message: string, ...args: any[]) {
    console.warn(`[MindMapSync Warning] ${message}`, ...args);
  }
  
  error(message: string, error?: any) {
    console.error(`[MindMapSync Error] ${message}`, error);
  }
}

/**
 * 错误处理器
 */
export class ErrorHandler {
  private logger: Logger;
  
  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  handle(error: any, context: string = 'Unknown'): void {
    this.logger.error(`Error in ${context}:`, error);
    
    // 可以在这里添加错误上报逻辑
    if (error instanceof Error) {
      this.logger.error(`Stack trace: ${error.stack}`);
    }
  }
  
  async handleAsync<T>(
    operation: () => Promise<T>,
    context: string = 'Unknown'
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      this.handle(error, context);
      return null;
    }
  }
}

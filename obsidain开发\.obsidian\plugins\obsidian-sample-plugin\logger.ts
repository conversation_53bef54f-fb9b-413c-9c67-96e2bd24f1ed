/**
 * 思维导图插件的日志系统
 * 提供统一的日志记录功能，可以根据需要启用或禁用不同级别的日志
 */

// 日志级别枚举
export enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3,
    TRACE = 4
}

// 默认日志级别
let currentLogLevel = LogLevel.INFO;

/**
 * 日志记录器类
 */
export class Logger {
    private context: string;

    /**
     * 创建一个新的日志记录器实例
     * @param context 日志上下文名称
     */
    constructor(context: string) {
        this.context = context;
    }

    /**
     * 记录错误级别的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    error(message: string, ...args: any[]): void {
        if (currentLogLevel >= LogLevel.ERROR) {
            console.error(`[ERROR][${this.context}] ${message}`, ...args);
        }
    }

    /**
     * 记录警告级别的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    warn(message: string, ...args: any[]): void {
        if (currentLogLevel >= LogLevel.WARN) {
            console.warn(`[WARN][${this.context}] ${message}`, ...args);
        }
    }

    /**
     * 记录信息级别的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    info(message: string, ...args: any[]): void {
        if (currentLogLevel >= LogLevel.INFO) {
            console.info(`[INFO][${this.context}] ${message}`, ...args);
        }
    }

    /**
     * 记录调试级别的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    debug(message: string, ...args: any[]): void {
        if (currentLogLevel >= LogLevel.DEBUG) {
            console.debug(`[DEBUG][${this.context}] ${message}`, ...args);
        }
    }

    /**
     * 记录跟踪级别的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    trace(message: string, ...args: any[]): void {
        if (currentLogLevel >= LogLevel.TRACE) {
            console.log(`[TRACE][${this.context}] ${message}`, ...args);
        }
    }
}

/**
 * 设置全局日志级别
 * @param level 日志级别
 */
export function setLogLevel(level: LogLevel): void {
    currentLogLevel = level;
}

/**
 * 获取当前日志级别
 * @returns 当前日志级别
 */
export function getLogLevel(): LogLevel {
    return currentLogLevel;
}

/**
 * 创建一个新的日志记录器
 * @param context 日志上下文名称
 * @returns 日志记录器实例
 */
export function createLogger(context: string): Logger {
    return new Logger(context);
}
/**
 * Markdown文档解析器
 * 负责解析Markdown文档结构，生成节点树
 */

import { NodeData, Position } from '../types';
import { 
  generateId, 
  parseHeadingLevel, 
  parseListLevel, 
  isCodeBlock, 
  cleanMarkdown,
  Logger 
} from '../utils';

export class MarkdownParser {
  private logger: Logger;
  
  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  /**
   * 解析Markdown文档，生成节点树
   */
  parse(content: string): NodeData[] {
    this.logger.debug('开始解析Markdown文档');
    
    const lines = content.split('\n');
    const nodes: NodeData[] = [];
    const stack: NodeData[] = [];
    let inCodeBlock = false;
    let codeBlockNode: NodeData | null = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // 跳过空行和注释
      if (!trimmedLine || trimmedLine.startsWith('<!--')) {
        continue;
      }
      
      // 处理代码块
      if (isCodeBlock(trimmedLine)) {
        if (!inCodeBlock) {
          // 开始代码块
          inCodeBlock = true;
          codeBlockNode = {
            id: generateId(),
            content: trimmedLine,
            level: 0,
            type: 'code',
            children: [],
            position: { line: i, ch: 0 }
          };
        } else {
          // 结束代码块
          if (codeBlockNode) {
            codeBlockNode.content += '\n' + trimmedLine;
            this.addNodeToTree(nodes, stack, codeBlockNode);
          }
          inCodeBlock = false;
          codeBlockNode = null;
        }
        continue;
      }
      
      // 在代码块内部
      if (inCodeBlock && codeBlockNode) {
        codeBlockNode.content += '\n' + line;
        continue;
      }
      
      // 解析标题
      const headingLevel = parseHeadingLevel(line);
      if (headingLevel > 0) {
        const node: NodeData = {
          id: generateId(),
          content: cleanMarkdown(line),
          level: headingLevel,
          type: 'heading',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        this.addNodeToTree(nodes, stack, node);
        continue;
      }
      
      // 解析列表
      const listLevel = parseListLevel(line);
      if (listLevel > 0) {
        const node: NodeData = {
          id: generateId(),
          content: cleanMarkdown(line),
          level: listLevel,
          type: 'list',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        this.addNodeToTree(nodes, stack, node);
        continue;
      }
      
      // 处理普通段落（作为前一个节点的子节点）
      if (trimmedLine && stack.length > 0) {
        const parentNode = stack[stack.length - 1];
        const paragraphNode: NodeData = {
          id: generateId(),
          content: trimmedLine,
          level: parentNode.level + 1,
          type: 'list',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        parentNode.children.push(paragraphNode);
      }
    }
    
    this.logger.debug(`解析完成，生成 ${nodes.length} 个根节点`);
    return nodes;
  }
  
  /**
   * 将节点添加到树结构中
   */
  private addNodeToTree(nodes: NodeData[], stack: NodeData[], node: NodeData): void {
    // 清理栈，移除层级大于等于当前节点的节点
    while (stack.length > 0 && stack[stack.length - 1].level >= node.level) {
      stack.pop();
    }
    
    if (stack.length === 0) {
      // 根节点
      nodes.push(node);
    } else {
      // 子节点
      const parent = stack[stack.length - 1];
      parent.children.push(node);
    }
    
    stack.push(node);
  }
  
  /**
   * 将节点树转换回Markdown文本
   */
  toMarkdown(nodes: NodeData[]): string {
    const lines: string[] = [];
    
    for (const node of nodes) {
      this.nodeToMarkdown(node, lines);
    }
    
    return lines.join('\n');
  }
  
  /**
   * 将单个节点转换为Markdown行
   */
  private nodeToMarkdown(node: NodeData, lines: string[]): void {
    let prefix = '';
    
    switch (node.type) {
      case 'heading':
        prefix = '#'.repeat(node.level) + ' ';
        break;
      case 'list':
        const indent = '  '.repeat(Math.max(0, node.level - 1));
        prefix = indent + '- ';
        break;
      case 'code':
        lines.push(node.content);
        return;
    }
    
    lines.push(prefix + node.content);
    
    // 递归处理子节点
    for (const child of node.children) {
      this.nodeToMarkdown(child, lines);
    }
  }
  
  /**
   * 查找指定位置的节点
   */
  findNodeByPosition(nodes: NodeData[], position: Position): NodeData | null {
    for (const node of nodes) {
      if (node.position && 
          node.position.line === position.line) {
        return node;
      }
      
      const childResult = this.findNodeByPosition(node.children, position);
      if (childResult) {
        return childResult;
      }
    }
    
    return null;
  }
  
  /**
   * 获取节点的路径
   */
  getNodePath(nodes: NodeData[], targetId: string): NodeData[] {
    const path: NodeData[] = [];
    
    const findPath = (currentNodes: NodeData[], currentPath: NodeData[]): boolean => {
      for (const node of currentNodes) {
        const newPath = [...currentPath, node];
        
        if (node.id === targetId) {
          path.push(...newPath);
          return true;
        }
        
        if (findPath(node.children, newPath)) {
          return true;
        }
      }
      return false;
    };
    
    findPath(nodes, []);
    return path;
  }
  
  /**
   * 计算节点树的差异
   */
  calculateDiff(oldNodes: NodeData[], newNodes: NodeData[]): {
    added: NodeData[];
    removed: NodeData[];
    modified: NodeData[];
  } {
    const added: NodeData[] = [];
    const removed: NodeData[] = [];
    const modified: NodeData[] = [];
    
    const oldNodeMap = new Map<string, NodeData>();
    const newNodeMap = new Map<string, NodeData>();
    
    // 构建节点映射
    this.buildNodeMap(oldNodes, oldNodeMap);
    this.buildNodeMap(newNodes, newNodeMap);
    
    // 查找新增和修改的节点
    for (const [id, newNode] of newNodeMap) {
      const oldNode = oldNodeMap.get(id);
      if (!oldNode) {
        added.push(newNode);
      } else if (oldNode.content !== newNode.content || 
                 oldNode.level !== newNode.level) {
        modified.push(newNode);
      }
    }
    
    // 查找删除的节点
    for (const [id, oldNode] of oldNodeMap) {
      if (!newNodeMap.has(id)) {
        removed.push(oldNode);
      }
    }
    
    return { added, removed, modified };
  }
  
  /**
   * 构建节点ID映射
   */
  private buildNodeMap(nodes: NodeData[], map: Map<string, NodeData>): void {
    for (const node of nodes) {
      map.set(node.id, node);
      this.buildNodeMap(node.children, map);
    }
  }
}

import {
	<PERSON><PERSON>,
	Editor,
	MarkdownView,
	Modal,
	Notice,
	Plugin,
	PluginSettingTab,
	Setting,
	WorkspaceLeaf
} from 'obsidian';

// 导入自定义模块
import { MindMapSyncSettings, DEFAULT_SETTINGS } from './src/types';
import { MindMapView, MINDMAP_VIEW_TYPE } from './src/ui/MindMapView';
import { Lo<PERSON>, ErrorHandler } from './src/utils';

export default class MindMapSyncPlugin extends Plugin {
	settings: MindMapSyncSettings;
	logger: Logger;
	private errorHandler: ErrorHandler;

	async onload() {
		console.log('加载智能思维导图同步插件');

		// 加载设置
		await this.loadSettings();

		// 初始化日志和错误处理
		this.logger = new Logger(this.settings.debugMode);
		this.errorHandler = new ErrorHandler(this.logger);

		// 注册思维导图视图
		this.registerView(
			MINDMAP_VIEW_TYPE,
			(leaf) => new MindMapView(leaf, this)
		);

		// 添加左侧面板图标
		const ribbonIconEl = this.addRibbonIcon('git-branch', '智能思维导图', (evt: MouseEvent) => {
			this.activateMindMapView();
		});
		ribbonIconEl.addClass('mindmap-ribbon-icon');

		// 注册命令
		this.registerCommands();

		// 添加设置选项卡
		this.addSettingTab(new MindMapSyncSettingTab(this.app, this));

		this.logger.info('智能思维导图同步插件加载完成');
	}

	onunload() {
		this.logger.info('智能思维导图同步插件卸载');
	}

	/**
	 * 激活思维导图视图
	 */
	async activateMindMapView() {
		const { workspace } = this.app;

		let leaf: WorkspaceLeaf | null = null;
		const leaves = workspace.getLeavesOfType(MINDMAP_VIEW_TYPE);

		if (leaves.length > 0) {
			// 如果已存在思维导图视图，激活它
			leaf = leaves[0];
		} else {
			// 创建新的思维导图视图
			leaf = workspace.getRightLeaf(false);
			if (leaf) {
				await leaf.setViewState({ type: MINDMAP_VIEW_TYPE, active: true });
			}
		}

		if (leaf) {
			workspace.revealLeaf(leaf);
		}
	}

	/**
	 * 注册插件命令
	 */
	private registerCommands() {
		// 打开思维导图视图
		this.addCommand({
			id: 'open-mindmap-view',
			name: '打开思维导图视图',
			callback: () => {
				this.activateMindMapView();
			}
		});

		// AI提问命令
		this.addCommand({
			id: 'ai-ask-question',
			name: 'AI提问',
			editorCallback: (editor: Editor, view: MarkdownView) => {
				this.handleAIQuestion(editor, view);
			}
		});

		// 刷新思维导图
		this.addCommand({
			id: 'refresh-mindmap',
			name: '刷新思维导图',
			callback: () => {
				this.refreshMindMap();
			}
		});

		// 切换同步状态
		this.addCommand({
			id: 'toggle-sync',
			name: '切换同步状态',
			callback: () => {
				this.settings.enableSync = !this.settings.enableSync;
				this.saveSettings();
				new Notice(`思维导图同步已${this.settings.enableSync ? '开启' : '关闭'}`);
			}
		});
	}

	/**
	 * 处理AI提问
	 */
	private async handleAIQuestion(editor: Editor, view: MarkdownView) {
		const selectedText = editor.getSelection();
		if (!selectedText) {
			new Notice('请先选择一段文本');
			return;
		}

		// 这里暂时只显示提示，AI功能将在后续实现
		new Notice('AI提问功能正在开发中...');
		this.logger.debug('AI提问请求', { selectedText });
	}

	/**
	 * 刷新思维导图
	 */
	private async refreshMindMap() {
		const leaves = this.app.workspace.getLeavesOfType(MINDMAP_VIEW_TYPE);
		for (const leaf of leaves) {
			const view = leaf.view as MindMapView;
			await view.refresh();
		}
		new Notice('思维导图已刷新');
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}

class MindMapSyncSettingTab extends PluginSettingTab {
	plugin: MindMapSyncPlugin;

	constructor(app: App, plugin: MindMapSyncPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const {containerEl} = this;
		containerEl.empty();

		containerEl.createEl('h2', {text: '智能思维导图同步设置'});

		// AI服务配置区
		containerEl.createEl('h3', {text: 'AI服务配置'});

		new Setting(containerEl)
			.setName('AI服务提供商')
			.setDesc('选择AI服务提供商')
			.addDropdown(dropdown => dropdown
				.addOption('ollama', 'Ollama (本地)')
				.addOption('openai', 'OpenAI')
				.setValue(this.plugin.settings.aiProvider)
				.onChange(async (value: 'openai' | 'ollama') => {
					this.plugin.settings.aiProvider = value;
					await this.plugin.saveSettings();
					this.display(); // 重新显示以更新相关设置
				}));

		if (this.plugin.settings.aiProvider === 'openai') {
			new Setting(containerEl)
				.setName('OpenAI API密钥')
				.setDesc('输入您的OpenAI API密钥')
				.addText(text => text
					.setPlaceholder('sk-...')
					.setValue(this.plugin.settings.openaiApiKey)
					.onChange(async (value) => {
						this.plugin.settings.openaiApiKey = value;
						await this.plugin.saveSettings();
					}));

			new Setting(containerEl)
				.setName('OpenAI模型')
				.setDesc('选择要使用的OpenAI模型')
				.addDropdown(dropdown => dropdown
					.addOption('gpt-4', 'GPT-4')
					.addOption('gpt-3.5-turbo', 'GPT-3.5 Turbo')
					.setValue(this.plugin.settings.openaiModel)
					.onChange(async (value) => {
						this.plugin.settings.openaiModel = value;
						await this.plugin.saveSettings();
					}));
		} else {
			new Setting(containerEl)
				.setName('Ollama服务地址')
				.setDesc('本地Ollama服务的地址')
				.addText(text => text
					.setPlaceholder('http://localhost:11434')
					.setValue(this.plugin.settings.ollamaUrl)
					.onChange(async (value) => {
						this.plugin.settings.ollamaUrl = value;
						await this.plugin.saveSettings();
					}));

			new Setting(containerEl)
				.setName('Ollama模型')
				.setDesc('要使用的Ollama模型名称')
				.addText(text => text
					.setPlaceholder('llama2')
					.setValue(this.plugin.settings.ollamaModel)
					.onChange(async (value) => {
						this.plugin.settings.ollamaModel = value;
						await this.plugin.saveSettings();
					}));
		}

		// 同步设置区
		containerEl.createEl('h3', {text: '同步设置'});

		new Setting(containerEl)
			.setName('启用双向同步')
			.setDesc('自动同步Markdown文档和思维导图的变更')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableSync)
				.onChange(async (value) => {
					this.plugin.settings.enableSync = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('同步延迟 (毫秒)')
			.setDesc('防抖延迟时间，避免频繁更新')
			.addSlider(slider => slider
				.setLimits(100, 1000, 50)
				.setValue(this.plugin.settings.debounceTime)
				.setDynamicTooltip()
				.onChange(async (value) => {
					this.plugin.settings.debounceTime = value;
					await this.plugin.saveSettings();
				}));

		// 高级设置区
		containerEl.createEl('h3', {text: '高级设置'});

		new Setting(containerEl)
			.setName('调试模式')
			.setDesc('启用详细的调试日志')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.debugMode)
				.onChange(async (value) => {
					this.plugin.settings.debugMode = value;
					this.plugin.logger?.setDebugMode(value);
					await this.plugin.saveSettings();
				}));
	}
}

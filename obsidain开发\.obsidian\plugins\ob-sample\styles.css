/*
智能思维导图同步插件样式文件
*/

/* 思维导图视图容器 */
.mindmap-view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-primary);
}

/* 思维导图工具栏 */
.mindmap-toolbar {
  background: var(--background-secondary);
  border-bottom: 1px solid var(--background-modifier-border);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.mindmap-toolbar button {
  padding: 4px 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.mindmap-toolbar button:hover {
  background: var(--background-modifier-hover);
}

.mindmap-toolbar button.mod-cta {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

.mindmap-toolbar .mindmap-file-label {
  margin-left: auto;
  font-size: 12px;
  color: var(--text-muted);
  font-style: italic;
}

/* 思维导图容器 */
.mindmap-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--background-primary);
}

/* 思维导图节点样式 */
.mindmap-container .mm-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.mindmap-container .mm-node:hover {
  opacity: 0.8;
}

.mindmap-container .mm-node-highlighted {
  stroke: var(--interactive-accent) !important;
  stroke-width: 2px !important;
}

/* AI生成节点特殊样式 */
.mindmap-container .mm-node-ai {
  stroke: var(--color-green) !important;
  fill: var(--color-green-rgb) !important;
}

.mindmap-container .mm-node-ai::after {
  content: "🤖";
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 10px;
}

/* 思维导图链接样式 */
.mindmap-container .mm-link {
  stroke: var(--text-muted);
  stroke-width: 1.5px;
  fill: none;
}

/* 左侧面板图标 */
.mindmap-ribbon-icon {
  color: var(--text-normal);
}

.mindmap-ribbon-icon:hover {
  color: var(--interactive-accent);
}

/* 加载状态 */
.mindmap-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 14px;
}

.mindmap-loading::before {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid var(--background-modifier-border);
  border-top: 2px solid var(--interactive-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.mindmap-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-error);
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

/* AI提问模态框样式 */
.ai-question-modal .modal-content {
  max-width: 500px;
}

.ai-question-modal .modal-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-normal);
}

.ai-question-modal .selected-text {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  font-family: var(--font-monospace);
  font-size: 13px;
  color: var(--text-muted);
  max-height: 100px;
  overflow-y: auto;
}

.ai-question-modal .question-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-text);
  font-size: 14px;
  resize: vertical;
  margin-bottom: 16px;
}

.ai-question-modal .question-input:focus {
  outline: none;
  border-color: var(--interactive-accent);
}

.ai-question-modal .modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.ai-question-modal .modal-buttons button {
  padding: 8px 16px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ai-question-modal .modal-buttons button:hover {
  background: var(--background-modifier-hover);
}

.ai-question-modal .modal-buttons button.mod-cta {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

/* AI回答样式 */
.ai-answer-block {
  border-left: 4px solid var(--color-green);
  background: var(--background-secondary);
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 0 4px 4px 0;
}

.ai-answer-block .ai-answer-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-muted);
}

.ai-answer-block .ai-answer-header::before {
  content: "🤖";
  font-size: 14px;
}

.ai-answer-block .ai-answer-content {
  color: var(--text-normal);
  line-height: 1.6;
}

/* 设置页面样式 */
.mindmap-settings h2 {
  color: var(--text-normal);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: 8px;
}

.mindmap-settings h3 {
  color: var(--text-normal);
  font-size: 16px;
  font-weight: 500;
  margin: 24px 0 12px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mindmap-toolbar {
    flex-wrap: wrap;
    gap: 4px;
  }

  .mindmap-toolbar button {
    font-size: 11px;
    padding: 3px 6px;
  }

  .ai-question-modal .modal-content {
    max-width: 90vw;
    margin: 20px;
  }
}
